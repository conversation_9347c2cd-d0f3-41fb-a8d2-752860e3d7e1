globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/utils/request.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                TokenManager: function() {
                    return TokenManager;
                },
                apiRequest: function() {
                    return apiRequest;
                },
                // 导出默认请求实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _umirequest = __mako_require__("node_modules/umi-request/dist/index.esm.js");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            // 配置 message 全局设置
            _antd.message.config({
                top: 100,
                duration: 3,
                maxCount: 3,
                rtl: false
            });
            // 创建请求实例
            const request = (0, _umirequest.extend)({
                prefix: '/api',
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            /**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */ class TokenManager {
                static TOKEN_KEY = 'auth_token';
                /**
   * 获取当前Token
   */ static getToken() {
                    return localStorage.getItem(TokenManager.TOKEN_KEY);
                }
                /**
   * 设置Token
   */ static setToken(token) {
                    localStorage.setItem(TokenManager.TOKEN_KEY, token);
                }
                /**
   * 清除Token
   */ static clearToken() {
                    localStorage.removeItem(TokenManager.TOKEN_KEY);
                }
                /**
   * 检查是否有Token
   */ static hasToken() {
                    return !!TokenManager.getToken();
                }
            }
            /**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */ request.interceptors.request.use((url, options)=>{
                const token = TokenManager.getToken();
                if (token) {
                    // 添加Authorization头部
                    const headers = {
                        ...options.headers,
                        Authorization: `Bearer ${token}`
                    };
                    return {
                        url,
                        options: {
                            ...options,
                            headers
                        }
                    };
                }
                return {
                    url,
                    options
                };
            });
            /**
 * HTTP状态码错误消息映射
 * 为常见的HTTP状态码提供用户友好的中文错误消息
 */ const HTTP_ERROR_MESSAGES = {
                400: '请求参数错误',
                401: '登录已过期，请重新登录',
                403: '没有权限访问该资源',
                404: '请求的资源不存在',
                405: '请求方法不被允许',
                408: '请求超时',
                409: '请求冲突',
                422: '请求数据验证失败',
                429: '请求过于频繁，请稍后重试',
                500: '服务器内部错误',
                502: '网关错误',
                503: '服务暂时不可用',
                504: '网关超时'
            };
            /**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 处理HTTP 200状态码下的业务错误（code !== 200）
 * - 处理非200 HTTP状态码的错误情况
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 */ // 统一的响应拦截器
            request.interceptors.response.use((response)=>{
                // 对于umi-request，响应数据在response.data中
                const data = response.data;
                // 处理HTTP 200状态码的情况
                if (response.status === 200) {
                    // 检查API响应是否包含业务错误（code字段不等于200）
                    if ((data === null || data === void 0 ? void 0 : data.code) !== undefined && data.code !== 200) {
                        console.log('检测到API业务错误:', data);
                        // 使用Ant Design message组件显示错误消息
                        const errorMessage = data.message || '请求失败';
                        console.log('准备显示错误消息:', errorMessage);
                        // 尝试显示错误消息
                        try {
                            // 使用 setTimeout 确保在下一个事件循环中执行
                            setTimeout(()=>{
                                const messageInstance = _antd.message.error(errorMessage);
                                console.log('message.error 调用结果:', messageInstance);
                            }, 0);
                            // 同时也立即调用一次
                            const messageInstance = _antd.message.error(errorMessage);
                            console.log('message.error 立即调用结果:', messageInstance);
                        } catch (msgError) {
                            console.error('message.error 调用失败:', msgError);
                            // 如果 message 组件失败，尝试使用原生 alert 作为备选
                            alert(`错误: ${errorMessage}`);
                        }
                        // 抛出错误以便调用方知道请求失败
                        throw new Error(errorMessage);
                    }
                    // 成功情况，返回响应
                    return response;
                }
                // 处理非200 HTTP状态码的情况
                // 这种情况通常不会到达这里，而是会进入catch处理
                // 但为了完整性，我们也处理一下
                const errorMessage = (data === null || data === void 0 ? void 0 : data.message) || HTTP_ERROR_MESSAGES[response.status] || `HTTP错误 ${response.status}`;
                _antd.message.error(errorMessage);
                throw new Error(errorMessage);
            });
            // 配置全局错误处理器
            request.extendOptions({
                errorHandler: (error)=>{
                    // 处理网络错误或HTTP非200状态码错误
                    if (error.response) {
                        const { status, data } = error.response;
                        // 特殊处理401认证失效
                        if (status === 401) {
                            TokenManager.clearToken();
                            _antd.message.error('登录已过期，请重新登录');
                            if (window.location.pathname !== '/user/login') window.location.href = '/user/login';
                            return;
                        }
                        // 特殊处理403权限错误
                        if (status === 403) {
                            // 检查是否是团队访问被拒绝的特殊错误
                            const errorMessage = data === null || data === void 0 ? void 0 : data.message;
                            if ((errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('停用')) || (errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('禁用')) || (errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('不是该团队的成员'))) // 团队访问相关的错误，使用后端返回的具体消息
                            _antd.message.error(errorMessage);
                            else // 其他权限错误，使用默认消息
                            _antd.message.error(HTTP_ERROR_MESSAGES[403]);
                            return;
                        }
                        // 处理其他HTTP状态码错误
                        let errorMessage;
                        // 优先使用后端返回的错误消息
                        if (data === null || data === void 0 ? void 0 : data.message) errorMessage = data.message;
                        else // 使用预定义的错误消息映射
                        errorMessage = HTTP_ERROR_MESSAGES[status] || `请求失败 (${status})`;
                        _antd.message.error(errorMessage);
                    } else if (error.request) // 请求已发出但没有收到响应（网络错误）
                    _antd.message.error('网络错误，请检查网络连接');
                    else // 其他错误（请求配置错误等）
                    _antd.message.error(error.message || '请求失败');
                }
            });
            const apiRequest = {
                get: (url, params)=>{
                    return request.get(url, {
                        params
                    });
                },
                post: (url, data)=>{
                    return request.post(url, {
                        data
                    });
                },
                put: (url, data)=>{
                    return request.put(url, {
                        data
                    });
                },
                delete: (url, params)=>{
                    return request.delete(url, {
                        params
                    });
                }
            };
            var _default = request;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '11521898861171342056';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.5934328244805619426.hot-update.js.map