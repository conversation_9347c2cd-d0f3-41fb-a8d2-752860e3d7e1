{"version": 3, "sources": ["umi.14701885334327591822.hot-update.js", "src/utils/request.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='4191751224933777913';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 请求工具类\n * 基于 umi-request 封装，支持双阶段认证\n */\n\nimport { message } from 'antd';\nimport { extend } from 'umi-request';\nimport type { ApiResponse } from '@/types/api';\n\n// 配置 message 全局设置\nmessage.config({\n  top: 100,\n  duration: 3,\n  maxCount: 3,\n  rtl: false,\n});\n \n\n// 创建请求实例\nconst request = extend({\n  prefix: '/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n/**\n * Token 管理器（单令牌系统）\n *\n * 功能说明：\n * - 统一管理用户认证Token的存储和获取\n * - 使用localStorage进行持久化存储\n * - 支持Token的设置、获取、清除和检查\n *\n * 设计理念：\n * - 采用单令牌系统，简化认证流程\n * - Token同时用于用户认证和团队访问\n * - 提供静态方法，便于全局调用\n */\nclass TokenManager {\n  private static readonly TOKEN_KEY = 'auth_token';\n\n  /**\n   * 获取当前Token\n   */\n  static getToken(): string | null {\n    return localStorage.getItem(TokenManager.TOKEN_KEY);\n  }\n\n  /**\n   * 设置Token\n   */\n  static setToken(token: string): void {\n    localStorage.setItem(TokenManager.TOKEN_KEY, token);\n  }\n\n  /**\n   * 清除Token\n   */\n  static clearToken(): void {\n    localStorage.removeItem(TokenManager.TOKEN_KEY);\n  }\n\n  /**\n   * 检查是否有Token\n   */\n  static hasToken(): boolean {\n    return !!TokenManager.getToken();\n  }\n}\n\n/**\n * 请求拦截器\n *\n * 功能：\n * - 自动在请求头中添加Authorization Bearer Token\n * - 统一处理认证信息的注入\n * - 支持无Token的公开接口访问\n */\nrequest.interceptors.request.use((url, options) => {\n  const token = TokenManager.getToken();\n\n  if (token) {\n    // 添加Authorization头部\n    const headers = {\n      ...options.headers,\n      Authorization: `Bearer ${token}`,\n    };\n    return {\n      url,\n      options: { ...options, headers },\n    };\n  }\n\n  return { url, options };\n});\n\n/**\n * HTTP状态码错误消息映射\n * 为常见的HTTP状态码提供用户友好的中文错误消息\n */\nconst HTTP_ERROR_MESSAGES: Record<number, string> = {\n  400: '请求参数错误',\n  401: '登录已过期，请重新登录',\n  403: '没有权限访问该资源',\n  404: '请求的资源不存在',\n  405: '请求方法不被允许',\n  408: '请求超时',\n  409: '请求冲突',\n  422: '请求数据验证失败',\n  429: '请求过于频繁，请稍后重试',\n  500: '服务器内部错误',\n  502: '网关错误',\n  503: '服务暂时不可用',\n  504: '网关超时',\n};\n\n/**\n * 响应拦截器\n *\n * 功能：\n * - 统一处理API响应格式\n * - 处理HTTP 200状态码下的业务错误（code !== 200）\n * - 处理非200 HTTP状态码的错误情况\n * - 自动处理认证失效情况\n * - 统一的错误消息提示\n * - 自动跳转到登录页面\n */\n// 统一的响应拦截器\nrequest.interceptors.response.use((response: any) => {\n  // 对于umi-request，响应数据在response.data中\n  const data = response.data;\n\n  // 处理HTTP 200状态码的情况\n  if (response.status === 200) {\n    // 检查API响应是否包含业务错误（code字段不等于200）\n    if (data?.code !== undefined && data.code !== 200) {\n      console.log('检测到API业务错误:', data);\n\n      // 使用Ant Design message组件显示错误消息\n      const errorMessage = data.message || '请求失败';\n      console.log('显示错误消息:', errorMessage);\n      message.error(errorMessage);\n\n      // 抛出错误以便调用方知道请求失败\n      throw new Error(errorMessage);\n    }\n\n    // 成功情况，返回响应\n    return response;\n  }\n\n  // 处理非200 HTTP状态码的情况\n  // 这种情况通常不会到达这里，而是会进入catch处理\n  // 但为了完整性，我们也处理一下\n  const errorMessage = data?.message || HTTP_ERROR_MESSAGES[response.status] || `HTTP错误 ${response.status}`;\n  message.error(errorMessage);\n  throw new Error(errorMessage);\n});\n\n// 配置全局错误处理器\nrequest.extendOptions({\n  errorHandler: (error: any) => {\n    // 处理网络错误或HTTP非200状态码错误\n    if (error.response) {\n      const { status, data } = error.response;\n\n      // 特殊处理401认证失效\n      if (status === 401) {\n        TokenManager.clearToken();\n        message.error('登录已过期，请重新登录');\n        if (window.location.pathname !== '/user/login') {\n          window.location.href = '/user/login';\n        }\n        return;\n      }\n\n      // 特殊处理403权限错误\n      if (status === 403) {\n        // 检查是否是团队访问被拒绝的特殊错误\n        const errorMessage = data?.message;\n        if (errorMessage?.includes('停用') || errorMessage?.includes('禁用') || errorMessage?.includes('不是该团队的成员')) {\n          // 团队访问相关的错误，使用后端返回的具体消息\n          message.error(errorMessage);\n        } else {\n          // 其他权限错误，使用默认消息\n          message.error(HTTP_ERROR_MESSAGES[403]);\n        }\n        return;\n      }\n\n      // 处理其他HTTP状态码错误\n      let errorMessage: string;\n\n      // 优先使用后端返回的错误消息\n      if (data?.message) {\n        errorMessage = data.message;\n      } else {\n        // 使用预定义的错误消息映射\n        errorMessage = HTTP_ERROR_MESSAGES[status] || `请求失败 (${status})`;\n      }\n\n      message.error(errorMessage);\n    } else if (error.request) {\n      // 请求已发出但没有收到响应（网络错误）\n      message.error('网络错误，请检查网络连接');\n    } else {\n      // 其他错误（请求配置错误等）\n      message.error(error.message || '请求失败');\n    }\n  },\n});\n\n// 封装常用的请求方法\nexport const apiRequest = {\n  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {\n    return request.get(url, { params });\n  },\n\n  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {\n    return request.post(url, { data });\n  },\n\n  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {\n    return request.put(url, { data });\n  },\n\n  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {\n    return request.delete(url, { params });\n  },\n};\n\n// 导出 Token 管理器\nexport { TokenManager };\n\n// 导出默认请求实例\nexport default request;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCuOJ,YAAY;2BAAZ;;gBAnBI,UAAU;2BAAV;;gBAqBb,WAAW;gBACX,OAAuB;2BAAvB;;;;;yCAxOwB;+CACD;;;;;;;;;YAGvB,kBAAkB;YAClB,aAAO,CAAC,MAAM,CAAC;gBACb,KAAK;gBACL,UAAU;gBACV,UAAU;gBACV,KAAK;YACP;YAGA,SAAS;YACT,MAAM,UAAU,IAAA,kBAAM,EAAC;gBACrB,QAAQ;gBACR,SAAS;gBACT,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA;;;;;;;;;;;;CAYC,GACD,MAAM;gBACJ,OAAwB,YAAY,aAAa;gBAEjD;;GAEC,GACD,OAAO,WAA0B;oBAC/B,OAAO,aAAa,OAAO,CAAC,aAAa,SAAS;gBACpD;gBAEA;;GAEC,GACD,OAAO,SAAS,KAAa,EAAQ;oBACnC,aAAa,OAAO,CAAC,aAAa,SAAS,EAAE;gBAC/C;gBAEA;;GAEC,GACD,OAAO,aAAmB;oBACxB,aAAa,UAAU,CAAC,aAAa,SAAS;gBAChD;gBAEA;;GAEC,GACD,OAAO,WAAoB;oBACzB,OAAO,CAAC,CAAC,aAAa,QAAQ;gBAChC;YACF;YAEA;;;;;;;CAOC,GACD,QAAQ,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK;gBACrC,MAAM,QAAQ,aAAa,QAAQ;gBAEnC,IAAI,OAAO;oBACT,oBAAoB;oBACpB,MAAM,UAAU;wBACd,GAAG,QAAQ,OAAO;wBAClB,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC;oBAClC;oBACA,OAAO;wBACL;wBACA,SAAS;4BAAE,GAAG,OAAO;4BAAE;wBAAQ;oBACjC;gBACF;gBAEA,OAAO;oBAAE;oBAAK;gBAAQ;YACxB;YAEA;;;CAGC,GACD,MAAM,sBAA8C;gBAClD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAEA;;;;;;;;;;CAUC,GACD,WAAW;YACX,QAAQ,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACjC,oCAAoC;gBACpC,MAAM,OAAO,SAAS,IAAI;gBAE1B,mBAAmB;gBACnB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,gCAAgC;oBAChC,IAAI,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,aAAa,KAAK,IAAI,KAAK,KAAK;wBACjD,QAAQ,GAAG,CAAC,eAAe;wBAE3B,+BAA+B;wBAC/B,MAAM,eAAe,KAAK,OAAO,IAAI;wBACrC,QAAQ,GAAG,CAAC,WAAW;wBACvB,aAAO,CAAC,KAAK,CAAC;wBAEd,kBAAkB;wBAClB,MAAM,IAAI,MAAM;oBAClB;oBAEA,YAAY;oBACZ,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,4BAA4B;gBAC5B,iBAAiB;gBACjB,MAAM,eAAe,CAAA,iBAAA,2BAAA,KAAM,OAAO,KAAI,mBAAmB,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,MAAM,CAAC,CAAC;gBACzG,aAAO,CAAC,KAAK,CAAC;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,YAAY;YACZ,QAAQ,aAAa,CAAC;gBACpB,cAAc,CAAC;oBACb,uBAAuB;oBACvB,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ;wBAEvC,cAAc;wBACd,IAAI,WAAW,KAAK;4BAClB,aAAa,UAAU;4BACvB,aAAO,CAAC,KAAK,CAAC;4BACd,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,eAC/B,OAAO,QAAQ,CAAC,IAAI,GAAG;4BAEzB;wBACF;wBAEA,cAAc;wBACd,IAAI,WAAW,KAAK;4BAClB,oBAAoB;4BACpB,MAAM,eAAe,iBAAA,2BAAA,KAAM,OAAO;4BAClC,IAAI,CAAA,yBAAA,mCAAA,aAAc,QAAQ,CAAC,WAAS,yBAAA,mCAAA,aAAc,QAAQ,CAAC,WAAS,yBAAA,mCAAA,aAAc,QAAQ,CAAC,cACzF,wBAAwB;4BACxB,aAAO,CAAC,KAAK,CAAC;iCAEd,gBAAgB;4BAChB,aAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI;4BAExC;wBACF;wBAEA,gBAAgB;wBAChB,IAAI;wBAEJ,gBAAgB;wBAChB,IAAI,iBAAA,2BAAA,KAAM,OAAO,EACf,eAAe,KAAK,OAAO;6BAE3B,eAAe;wBACf,eAAe,mBAAmB,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;wBAGlE,aAAO,CAAC,KAAK,CAAC;oBAChB,OAAO,IAAI,MAAM,OAAO,EACtB,qBAAqB;oBACrB,aAAO,CAAC,KAAK,CAAC;yBAEd,gBAAgB;oBAChB,aAAO,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;gBAEnC;YACF;YAGO,MAAM,aAAa;gBACxB,KAAK,CAAU,KAAa;oBAC1B,OAAO,QAAQ,GAAG,CAAC,KAAK;wBAAE;oBAAO;gBACnC;gBAEA,MAAM,CAAU,KAAa;oBAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK;wBAAE;oBAAK;gBAClC;gBAEA,KAAK,CAAU,KAAa;oBAC1B,OAAO,QAAQ,GAAG,CAAC,KAAK;wBAAE;oBAAK;gBACjC;gBAEA,QAAQ,CAAU,KAAa;oBAC7B,OAAO,QAAQ,MAAM,CAAC,KAAK;wBAAE;oBAAO;gBACtC;YACF;gBAMA,WAAe;;;;;;;;;;;;;;;;;;;;;;;ID1OD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}